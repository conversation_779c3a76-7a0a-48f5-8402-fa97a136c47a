<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ChatApp.Core\ChatApp.Core.csproj" />
    <ProjectReference Include="..\ChatApp.Services\ChatApp.Services.csproj" />
    <ProjectReference Include="..\ChatApp.EntityFrameworkCore\ChatApp.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\" />
  </ItemGroup>

</Project>
