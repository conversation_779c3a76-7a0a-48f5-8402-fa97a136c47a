# Render.com configuration for .NET Chat App
services:
  - type: web
    name: dotnet-chat-app
    env: docker
    dockerfilePath: ./Dockerfile
    dockerContext: .
    plan: starter
    region: oregon
    branch: master
    buildCommand: ""
    startCommand: ""
    healthCheckPath: /health
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:$PORT
      - key: DOTNET_RUNNING_IN_CONTAINER
        value: true
      - key: DOTNET_USE_POLLING_FILE_WATCHER
        value: true
      - key: ASPNETCORE_FORWARDEDHEADERS_ENABLED
        value: true
    # Add any database or external service environment variables here
    # Example:
    # - key: DATABASE_URL
    #   fromDatabase:
    #     name: chat-app-db
    #     property: connectionString
    
# Uncomment and configure if you need a database
# databases:
#   - name: chat-app-db
#     databaseName: chatapp
#     user: chatapp_user
#     plan: starter
#     region: oregon
