# Multi-stage Dockerfile for .NET 8.0 Chat App
# Optimized for Render.com deployment

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file and project files for dependency resolution
COPY ChatApp.Web/ChatApp.Web.sln ./
COPY ChatApp.Web/ChatApp.Web.csproj ChatApp.Web/
COPY ChatApp.Core/ChatApp.Core.csproj ChatApp.Core/
COPY ChatApp.Services/ChatApp.Services.csproj ChatApp.Services/
COPY ChatApp.EntityFrameworkCore/ChatApp.EntityFrameworkCore.csproj ChatApp.EntityFrameworkCore/

# Restore dependencies
RUN dotnet restore ChatApp.Web/ChatApp.Web.csproj

# Copy the entire source code
COPY . .

# Build the application
WORKDIR /src/ChatApp.Web
RUN dotnet build ChatApp.Web.csproj -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish ChatApp.Web.csproj -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Create a non-root user for security
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Copy published application
COPY --from=publish /app/publish .

# Configure environment for production
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:$PORT
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Expose port (Render will set the PORT environment variable)
EXPOSE $PORT

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:$PORT/health || exit 1

# Entry point
ENTRYPOINT ["dotnet", "ChatApp.Web.dll"]
