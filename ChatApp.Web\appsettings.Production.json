{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Console": {"IncludeScopes": false, "LogLevel": {"Default": "Information"}}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://+:${PORT:5000}"}}}, "ForwardedHeaders": {"ForwardedHeaders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,XForwardedProto", "KnownNetworks": [], "KnownProxies": []}, "HealthChecks": {"UI": {"HealthCheckDatabaseConnectionString": "", "EvaluationTimeOnSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}}}