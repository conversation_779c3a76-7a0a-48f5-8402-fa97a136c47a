# Docker Deployment Guide for .NET Chat App on Render.com

This guide explains how to deploy your .NET 8.0 Chat App to Render.com using Docker.

## 📋 Prerequisites

- Git repository with your .NET Chat App code
- Render.com account
- Docker (for local testing)

## 🐳 Docker Configuration

### Files Created

1. **Dockerfile** - Multi-stage Docker build optimized for .NET 8.0
2. **.dockerignore** - Excludes unnecessary files from Docker build context
3. **render.yaml** - Render.com service configuration
4. **appsettings.Production.json** - Production environment settings

### Docker Features

- **Multi-stage build**: Optimizes image size by separating build and runtime stages
- **Security**: Runs as non-root user
- **Health checks**: Built-in health monitoring
- **Environment variables**: Configurable for different environments
- **Port binding**: Dynamic port configuration for Render.com

## 🚀 Deployment Steps

### Option 1: Using render.yaml (Recommended)

1. **Push your code to GitHub**:
   ```bash
   git add .
   git commit -m "Add Docker configuration for Render deployment"
   git push origin main
   ```

2. **Connect to Render.com**:
   - Go to [Render.com](https://render.com)
   - Click "New +" → "Blueprint"
   - Connect your GitHub repository
   - Render will automatically detect the `render.yaml` file

3. **Configure environment variables** (if needed):
   - The render.yaml already includes basic environment variables
   - Add any additional variables in the Render dashboard

### Option 2: Manual Service Creation

1. **Create a new Web Service**:
   - Go to Render.com dashboard
   - Click "New +" → "Web Service"
   - Connect your GitHub repository

2. **Configure the service**:
   - **Environment**: Docker
   - **Build Command**: (leave empty)
   - **Start Command**: (leave empty)
   - **Dockerfile Path**: `./Dockerfile`
   - **Docker Context**: `.`

3. **Set environment variables**:
   ```
   ASPNETCORE_ENVIRONMENT=Production
   ASPNETCORE_URLS=http://+:$PORT
   DOTNET_RUNNING_IN_CONTAINER=true
   ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
   ```

## 🔧 Configuration Details

### Port Configuration

The application is configured to:
- Use the `PORT` environment variable provided by Render
- Default to port 5000 if PORT is not set
- Listen on all interfaces (`+`)

### Health Checks

- **Endpoint**: `/health`
- **Interval**: 30 seconds
- **Timeout**: 3 seconds
- **Retries**: 3

### Logging

Production logging is configured to:
- Log to console (required for Render)
- Information level for application logs
- Warning level for ASP.NET Core framework logs

## 🧪 Local Testing

Test the Docker configuration locally:

```bash
# Build the Docker image
docker build -t dotnet-chat-app .

# Run the container
docker run -p 5000:5000 -e PORT=5000 dotnet-chat-app

# Test the application
curl http://localhost:5000/health
curl http://localhost:5000/
```

## 🔍 Troubleshooting

### Common Issues

1. **Build failures**:
   - Check that all project references are correct
   - Ensure .dockerignore doesn't exclude necessary files
   - Verify .NET 8.0 SDK is available

2. **Runtime issues**:
   - Check environment variables are set correctly
   - Verify port configuration
   - Review application logs in Render dashboard

3. **Health check failures**:
   - Ensure `/health` endpoint is accessible
   - Check if the application is binding to the correct port
   - Verify no firewall or security group issues

### Debugging Commands

```bash
# Check container logs
docker logs <container-id>

# Access container shell
docker exec -it <container-id> /bin/bash

# Test health endpoint
curl http://localhost:$PORT/health
```

## 📝 Additional Configuration

### Database Integration

If you need a database, uncomment the database section in `render.yaml`:

```yaml
databases:
  - name: chat-app-db
    databaseName: chatapp
    user: chatapp_user
    plan: starter
    region: oregon
```

Then add the connection string environment variable:

```yaml
envVars:
  - key: DATABASE_URL
    fromDatabase:
      name: chat-app-db
      property: connectionString
```

### Custom Domain

1. Go to your service settings in Render
2. Navigate to "Custom Domains"
3. Add your domain and configure DNS

### SSL/TLS

Render automatically provides SSL certificates for all services. No additional configuration needed.

## 🎯 Performance Optimization

The Docker configuration includes several optimizations:

1. **Multi-stage build**: Reduces final image size
2. **Layer caching**: Optimizes build times
3. **Non-root user**: Improves security
4. **Health checks**: Enables automatic recovery
5. **Proper logging**: Facilitates debugging

## 📚 Next Steps

1. Set up monitoring and alerting
2. Configure CI/CD pipelines
3. Add database migrations (if using Entity Framework)
4. Implement proper error handling and logging
5. Set up backup strategies

## 🆘 Support

If you encounter issues:

1. Check Render.com documentation
2. Review application logs in Render dashboard
3. Test Docker configuration locally
4. Verify all environment variables are set correctly
